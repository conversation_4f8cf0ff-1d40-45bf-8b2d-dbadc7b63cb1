# 浏览器自动化流程配置工具

## 概述

这是一个专门用于管理浏览器配置文件与自动化流程绑定关系的GUI工具。通过直观的两栏布局界面，您可以轻松地为每个浏览器分配相应的自动化流程。

## 功能特性

- ✅ **浏览器列表展示**：在左侧表格中清晰展示所有指纹浏览器配置
- ✅ **流程分配**：为单个浏览器从预设的自动化流程列表中选择流程
- ✅ **配置保存**：点击保存后自动更新配置文件中的绑定关系
- ✅ **两栏布局**：左侧70%浏览器列表，右侧30%操作面板
- ✅ **无选择选项**：支持不为浏览器分配任何自动化流程
- ✅ **配置分离**：浏览器配置和自动化配置分开存储

## 启动方法

### 方法1：使用专门的流程配置工具（推荐）

```bash
python flow_config_app.py
```

### 方法2：使用完整的主应用程序

```bash
python main.py
```

然后在菜单中选择 "工具" -> "浏览器流程配置"

## 使用说明

### 1. 启动应用程序

运行 `python flow_config_app.py` 启动专门的配置工具。

### 2. 查看浏览器列表

- 左侧表格显示所有浏览器配置
- 包含：配置文件名称、浏览器ID、分组、当前绑定流程

### 3. 配置流程绑定

1. 在左侧表格中点击选择一个浏览器
2. 右侧面板会显示当前选中的浏览器信息
3. 在"自动化流程"下拉菜单中选择要分配的流程：
   - **无选择**：不分配任何自动化流程
   - **流程A：YouTube Shorts 自动化**：YouTube视频自动化
   - **流程B：Docker 网站访问**：自动访问指定网站
   - **流程C：数据采集**：数据采集自动化
4. 查看流程描述了解详细信息
5. 点击"保存配置"按钮保存设置

### 4. 刷新数据

- 点击右上角的"刷新数据"按钮
- 或使用菜单 "文件" -> "刷新数据"
- 或按 F5 键

### 5. 查看配置摘要

在菜单中选择 "工具" -> "配置摘要" 查看当前配置统计信息。

## 配置文件

### 主配置文件：`config/config.json`
包含浏览器基本信息和现有的自动化配置。

### 流程配置文件：`config/automation_flows.json`
包含：
- 可用的自动化流程定义
- 浏览器与流程的绑定关系

## 可用的自动化流程

1. **无选择** (`none`)
   - 不分配任何自动化流程
   - 适用于不需要自动化的浏览器

2. **YouTube Shorts 自动化** (`youtube_shorts`)
   - 自动观看、点赞、订阅 YouTube Shorts 视频
   - 适用于YouTube相关的自动化任务

3. **Docker 网站访问** (`docker_automation`)
   - 自动访问指定的网站列表
   - 适用于网站访问和保活任务

4. **数据采集** (`data_collection`)
   - 自动采集指定网站数据
   - 适用于数据抓取和分析任务

## 界面布局

```
┌─────────────────────────────────────────────────────────────────────┐
│ 浏览器自动化流程配置工具                                              │
├─────────────────────────────────────────────────────────────────────┤
│ 浏览器列表 (70%)                    │ 自动化流程配置 (30%)           │
│ ┌─────────────────────────────────┐ │ ┌─────────────────────────────┐ │
│ │ 配置文件名称 │ ID │ 分组 │ 流程 │ │ │ 当前选中: [浏览器名称]      │ │
│ ├─────────────────────────────────┤ │ │                             │ │
│ │ YouTube_猫咪 │ 1  │ YT  │ 流程A│ │ │ 自动化流程:                 │ │
│ │ 乡村1       │ 2  │ YT  │ 无   │ │ │ [下拉选择菜单]              │ │
│ │ ...         │... │ ... │ ... │ │ │                             │ │
│ └─────────────────────────────────┘ │ │ 流程描述:                   │ │
│                                     │ │ [描述文本区域]              │ │
│                                     │ │                             │ │
│                                     │ │      [保存配置]             │ │
│                                     │ └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────┘
```

## 注意事项

1. **配置保存**：每次修改后都需要点击"保存配置"按钮
2. **数据刷新**：如果外部修改了配置文件，需要手动刷新数据
3. **备份配置**：建议定期备份 `config/` 目录下的配置文件
4. **流程执行**：此工具只负责配置绑定关系，不负责执行自动化流程

## 故障排除

### 启动失败
- 确保已安装 PyQt6：`pip install PyQt6`
- 确保在项目根目录运行命令

### 数据加载失败
- 检查 `config/config.json` 文件是否存在且格式正确
- 检查 `config/automation_flows.json` 文件是否存在

### 保存失败
- 确保对 `config/` 目录有写入权限
- 检查配置文件是否被其他程序占用

## 技术支持

如有问题，请检查控制台输出的日志信息，或查看应用程序状态栏的提示信息。
