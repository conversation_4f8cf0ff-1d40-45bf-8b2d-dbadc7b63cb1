#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube 自动化浏览器配置管理工具
从 ixBrowser API 获取完整浏览器数据，支持 YouTube 自动化参与设置
"""

import sys
import json
import logging
import time
from typing import Dict, Any, List, Optional
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.browser_api import ixBrowserAPI
from utils.config_manager import ConfigManager
from utils.logger_setup import get_logger

# 设置日志
logger = get_logger("youtube_browser_config")


class MultiProjectBrowserConfigManager:
    """多项目自动化浏览器配置管理器"""

    def __init__(self):
        """初始化管理器"""
        self.browser_api = ixBrowserAPI()
        self.config_manager = ConfigManager()
        
    def check_api_connection(self) -> bool:
        """
        检查 API 连接
        
        Returns:
            bool: API 是否可用
        """
        print("正在检查 ixBrowser API 连接...")
        if self.browser_api.check_api_server():
            print("✅ ixBrowser API 连接正常")
            return True
        else:
            print("❌ ixBrowser API 连接失败")
            print("请确保 ixBrowser 软件正在运行")
            return False
    
    def fetch_all_browsers(self) -> Optional[List[Dict[str, Any]]]:
        """
        从 API 获取所有浏览器配置数据

        Returns:
            Optional[List[Dict[str, Any]]]: 完整的浏览器列表，失败时返回 None
        """
        print("正在从 ixBrowser API 获取完整浏览器数据...")

        try:
            # 获取第一页以确定总数
            browser_data = self.browser_api.get_browser_list(page=1, limit=50)
            if not browser_data:
                print("❌ 获取浏览器列表失败")
                return None

            total_count = browser_data.get('total', 0)
            if total_count == 0:
                print("❌ 没有找到浏览器配置")
                return None

            print(f"📊 发现 {total_count} 个浏览器配置，正在获取完整数据...")

            # 计算需要获取的页数
            limit = 100  # 每页获取更多数据
            total_pages = (total_count + limit - 1) // limit

            all_browsers = []

            # 获取所有页面的数据
            for page in range(1, total_pages + 1):
                page_data = self.browser_api.get_browser_list(page=page, limit=limit)
                if page_data and 'data' in page_data:
                    browsers = page_data['data']
                    all_browsers.extend(browsers)
                    print(f"📄 已获取第 {page}/{total_pages} 页数据 ({len(browsers)} 个浏览器)")

            print(f"✅ 成功获取 {len(all_browsers)} 个完整浏览器配置")
            return all_browsers

        except Exception as e:
            logger.error(f"获取浏览器数据时发生错误: {e}")
            print(f"❌ 获取浏览器数据时发生错误: {e}")
            return None
    
    def display_browser_list(self, browsers: List[Dict[str, Any]]) -> None:
        """
        显示完整的浏览器列表信息

        Args:
            browsers: 浏览器列表
        """
        print("\n📋 ixBrowser 完整浏览器数据:")
        print("=" * 100)

        # 按分组组织显示
        groups = {}
        for browser in browsers:
            group_name = browser.get('group_name', '未知分组')
            if group_name not in groups:
                groups[group_name] = []
            groups[group_name].append(browser)

        for group_name, group_browsers in groups.items():
            print(f"\n🏷️  分组: {group_name} ({len(group_browsers)} 个浏览器)")
            print("-" * 80)

            for i, browser in enumerate(group_browsers, 1):
                profile_id = browser.get('profile_id', '未知')
                name = browser.get('name', '未命名')
                note = browser.get('note', '')
                last_open = browser.get('last_open_time', 0)
                proxy_type = browser.get('proxy_type', '未知')

                # 格式化最后打开时间
                if last_open:
                    from datetime import datetime
                    last_open_str = datetime.fromtimestamp(last_open).strftime('%Y-%m-%d %H:%M')
                else:
                    last_open_str = '从未打开'

                print(f"  {i:2d}. ID: {str(profile_id):6s} | 名称: {name:15s} | 备注: {note:15s}")
                print(f"      代理: {proxy_type:8s} | 最后打开: {last_open_str}")
                print()
    
    def get_automation_settings(self, browsers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取多项目自动化参与设置

        Args:
            browsers: 浏览器列表

        Returns:
            Dict[str, Any]: 多项目自动化配置
        """
        print("\n🎯 多项目自动化参与设置")
        print("=" * 60)

        # 按分组组织
        groups = {}
        for browser in browsers:
            group_name = browser.get('group_name', '未知分组')
            if group_name not in groups:
                groups[group_name] = []
            groups[group_name].append(browser)

        # 初始化配置结构
        automation_config = {
            "youtube_automation": {
                "enabled_browsers": {},
                "group_settings": {}
            },
            "docker_automation": {
                "enabled_browsers": {},
                "group_settings": {}
            }
        }

        # 1. 分组级别设置
        print("\n📂 分组级别设置:")
        for group_name, group_browsers in groups.items():
            print(f"\n分组: {group_name} ({len(group_browsers)} 个浏览器)")

            # YouTube 自动化设置
            while True:
                choice = input(f"该分组是否用于 YouTube 自动化? (y/n/s=单独设置): ").strip().lower()

                if choice in ['y', 'yes']:
                    automation_config["youtube_automation"]["group_settings"][group_name] = {
                        "auto_include": True,
                        "project_type": "youtube"
                    }
                    # 为该分组所有浏览器设置参与
                    for browser in group_browsers:
                        profile_id = str(browser.get('profile_id', ''))
                        automation_config["youtube_automation"]["enabled_browsers"][profile_id] = {
                            "participate": True,
                            "note": f"自动包含 - {group_name}"
                        }
                    print(f"✅ 分组 '{group_name}' 的所有浏览器已设置为参与 YouTube 自动化")
                    break
                elif choice in ['n', 'no']:
                    automation_config["youtube_automation"]["group_settings"][group_name] = {
                        "auto_include": False,
                        "project_type": "other"
                    }
                    # 为该分组所有浏览器设置不参与
                    for browser in group_browsers:
                        profile_id = str(browser.get('profile_id', ''))
                        automation_config["youtube_automation"]["enabled_browsers"][profile_id] = {
                            "participate": False,
                            "note": f"分组排除 - {group_name}"
                        }
                    print(f"❌ 分组 '{group_name}' 的所有浏览器已设置为不参与 YouTube 自动化")
                    break
                elif choice in ['s', 'single']:
                    automation_config["youtube_automation"]["group_settings"][group_name] = {
                        "auto_include": False,
                        "project_type": "mixed"
                    }
                    # 单独设置每个浏览器
                    self._set_individual_browsers_for_project(group_browsers, automation_config["youtube_automation"]["enabled_browsers"], "YouTube")
                    break
                else:
                    print("❌ 请输入 y(是)/n(否)/s(单独设置)")

            # Docker 自动化设置
            print(f"\n分组: {group_name} - Docker 自动化设置")
            while True:
                choice = input(f"该分组是否用于 Docker 自动化? (y/n/s=单独设置): ").strip().lower()

                if choice in ['y', 'yes']:
                    automation_config["docker_automation"]["group_settings"][group_name] = {
                        "auto_include": True,
                        "project_type": "docker"
                    }
                    # 为该分组所有浏览器设置参与
                    for browser in group_browsers:
                        profile_id = str(browser.get('profile_id', ''))
                        automation_config["docker_automation"]["enabled_browsers"][profile_id] = {
                            "participate": True,
                            "note": f"自动包含 - {group_name}"
                        }
                    print(f"✅ 分组 '{group_name}' 的所有浏览器已设置为参与 Docker 自动化")
                    break
                elif choice in ['n', 'no']:
                    automation_config["docker_automation"]["group_settings"][group_name] = {
                        "auto_include": False,
                        "project_type": "other"
                    }
                    # 为该分组所有浏览器设置不参与
                    for browser in group_browsers:
                        profile_id = str(browser.get('profile_id', ''))
                        automation_config["docker_automation"]["enabled_browsers"][profile_id] = {
                            "participate": False,
                            "note": f"分组排除 - {group_name}"
                        }
                    print(f"❌ 分组 '{group_name}' 的所有浏览器已设置为不参与 Docker 自动化")
                    break
                elif choice in ['s', 'single']:
                    automation_config["docker_automation"]["group_settings"][group_name] = {
                        "auto_include": False,
                        "project_type": "mixed"
                    }
                    # 单独设置每个浏览器
                    self._set_individual_browsers_for_project(group_browsers, automation_config["docker_automation"]["enabled_browsers"], "Docker")
                    break
                else:
                    print("❌ 请输入 y(是)/n(否)/s(单独设置)")

        return automation_config

    def _set_individual_browsers_for_project(self, browsers: List[Dict[str, Any]], enabled_browsers: Dict[str, Any], project_name: str):
        """
        单独设置浏览器参与状态

        Args:
            browsers: 浏览器列表
            enabled_browsers: 启用浏览器配置字典
            project_name: 项目名称
        """
        print(f"\n🔧 单独设置浏览器 ({project_name} 自动化):")
        for browser in browsers:
            profile_id = str(browser.get('profile_id', ''))
            name = browser.get('name', '未命名')
            note = browser.get('note', '')

            print(f"\n浏览器: {name} (ID: {profile_id})")
            if note:
                print(f"备注: {note}")

            while True:
                choice = input(f"是否参与 {project_name} 自动化? (y/n): ").strip().lower()
                if choice in ['y', 'yes']:
                    enabled_browsers[profile_id] = {
                        "participate": True,
                        "note": f"手动选择 - {name}"
                    }
                    print("✅ 已设置为参与")
                    break
                elif choice in ['n', 'no']:
                    enabled_browsers[profile_id] = {
                        "participate": False,
                        "note": f"手动排除 - {name}"
                    }
                    print("❌ 已设置为不参与")
                    break
                else:
                    print("❌ 请输入 y(是) 或 n(否)")
    
    def save_automation_config(self, browsers: List[Dict[str, Any]], automation_config: Dict[str, Any]) -> bool:
        """
        保存多项目自动化配置

        Args:
            browsers: 完整浏览器列表
            automation_config: 多项目自动化配置

        Returns:
            bool: 是否保存成功
        """
        print(f"\n💾 正在保存多项目自动化配置...")

        try:
            # 创建新的配置结构
            new_config = {
                "browser_data": {
                    "last_sync": int(time.time()),
                    "total_browsers": len(browsers),
                    "browsers": {}
                },
                "youtube_automation": automation_config["youtube_automation"],
                "docker_automation": automation_config["docker_automation"],
                "docker_settings": {
                    "sites": [
                        {
                            "url": "https://chat.deepseek.com/",
                            "name": "DeepSeek Chat",
                            "wait_time": [3, 5]
                        },
                        {
                            "url": "https://jimeng.jianying.com/ai-tool/home",
                            "name": "剪映AI工具",
                            "wait_time": [3, 5]
                        }
                    ],
                    "browser_wait_between_sites": [2, 4],
                    "close_tabs_after_visit": True
                },
                "settings": {
                    "min_watch_time": 3,
                    "max_watch_time": 8,
                    "like_probability": 0.3,
                    "subscribe_probability": 0.1,
                    "scroll_count": 50
                },
                "api": {
                    "base_url": "http://127.0.0.1:53200",
                    "timeout": 30
                }
            }

            # 保存完整的浏览器数据
            for browser in browsers:
                profile_id = str(browser.get('profile_id', ''))
                if profile_id:
                    new_config["browser_data"]["browsers"][profile_id] = {
                        "profile_id": browser.get('profile_id'),
                        "name": browser.get('name', ''),
                        "note": browser.get('note', ''),
                        "group_id": browser.get('group_id'),
                        "group_name": browser.get('group_name', ''),
                        "last_open_time": browser.get('last_open_time', 0),
                        "proxy_type": browser.get('proxy_type', ''),
                        "color": browser.get('color', ''),
                        "tag_name": browser.get('tag_name', '')
                    }

            # 保存配置文件
            if self.config_manager.save_config(new_config):
                print(f"✅ 配置保存成功!")

                # 统计信息
                youtube_count = sum(1 for config in automation_config["youtube_automation"]["enabled_browsers"].values()
                                  if config.get("participate", False))
                docker_count = sum(1 for config in automation_config["docker_automation"]["enabled_browsers"].values()
                                 if config.get("participate", False))
                print(f"📊 配置统计:")
                print(f"   - 总浏览器数: {len(browsers)}")
                print(f"   - 参与 YouTube 自动化: {youtube_count}")
                print(f"   - 参与 Docker 自动化: {docker_count}")
                print(f"   - 不参与任何自动化: {len(browsers) - youtube_count - docker_count}")

                return True
            else:
                print(f"❌ 配置保存失败")
                return False

        except Exception as e:
            logger.error(f"保存配置时发生错误: {e}")
            print(f"❌ 保存失败: {e}")
            return False
    
    def run(self) -> None:
        """运行多项目自动化配置流程"""
        print("🎯 多项目自动化浏览器配置工具")
        print("=" * 60)

        # 检查 API 连接
        if not self.check_api_connection():
            return

        # 获取完整浏览器数据
        browsers = self.fetch_all_browsers()
        if not browsers:
            return

        # 显示浏览器列表
        self.display_browser_list(browsers)

        # 获取多项目自动化设置
        automation_config = self.get_automation_settings(browsers)

        # 显示配置摘要
        self._display_config_summary(automation_config)

        # 确认保存
        print(f"\n📝 即将保存多项目自动化配置")
        confirm = input("确认保存? (y/N): ").strip().lower()

        if confirm in ['y', 'yes']:
            if self.save_automation_config(browsers, automation_config):
                print("\n🎉 多项目自动化配置完成!")
                print("\n💡 提示:")
                print("   - 使用 'python sequential_automation.py' 开始 YouTube 自动化")
                print("   - 使用 'python docker_automation.py' 开始 Docker 自动化")
                print("   - 只有设置为参与的浏览器才会执行对应的自动化")
            else:
                print("\n❌ 配置保存失败!")
        else:
            print("\n❌ 用户取消配置")

    def _display_config_summary(self, automation_config: Dict[str, Any]):
        """显示配置摘要"""
        print("\n📋 配置摘要:")
        print("-" * 40)

        # YouTube 自动化统计
        youtube_participate = []
        youtube_exclude = []
        for profile_id, config in automation_config["youtube_automation"]["enabled_browsers"].items():
            if config.get("participate", False):
                youtube_participate.append(profile_id)
            else:
                youtube_exclude.append(profile_id)

        # Docker 自动化统计
        docker_participate = []
        docker_exclude = []
        for profile_id, config in automation_config["docker_automation"]["enabled_browsers"].items():
            if config.get("participate", False):
                docker_participate.append(profile_id)
            else:
                docker_exclude.append(profile_id)

        print(f"🎯 YouTube 自动化:")
        print(f"   ✅ 参与: {len(youtube_participate)} 个浏览器")
        print(f"   ❌ 不参与: {len(youtube_exclude)} 个浏览器")

        print(f"\n🐳 Docker 自动化:")
        print(f"   ✅ 参与: {len(docker_participate)} 个浏览器")
        print(f"   ❌ 不参与: {len(docker_exclude)} 个浏览器")

        # 显示分组设置
        print(f"\n📂 分组设置:")
        all_groups = set()
        all_groups.update(automation_config["youtube_automation"]["group_settings"].keys())
        all_groups.update(automation_config["docker_automation"]["group_settings"].keys())

        for group_name in all_groups:
            youtube_settings = automation_config["youtube_automation"]["group_settings"].get(group_name, {})
            docker_settings = automation_config["docker_automation"]["group_settings"].get(group_name, {})

            youtube_type = youtube_settings.get("project_type", "none")
            docker_type = docker_settings.get("project_type", "none")

            print(f"   - {group_name}: YouTube({youtube_type}) | Docker({docker_type})")


def main():
    """主函数"""
    try:
        config_manager = MultiProjectBrowserConfigManager()
        config_manager.run()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        logger.error(f"程序运行时发生未知错误: {e}")
        print(f"\n❌ 程序运行时发生未知错误: {e}")


if __name__ == "__main__":
    main()
