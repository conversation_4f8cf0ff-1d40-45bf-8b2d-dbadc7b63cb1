{"flows": {"youtube_shorts": {"id": "youtube_shorts", "name": "流程A：YouTube Shorts 自动化", "description": "自动观看、点赞、订阅 YouTube Shorts 视频", "type": "youtube", "enabled": true, "settings": {"min_watch_time": 3, "max_watch_time": 8, "like_probability": 0.3, "subscribe_probability": 0.1, "scroll_count": 50}}, "docker_automation": {"id": "docker_automation", "name": "流程B：Docker 网站访问", "description": "自动访问指定网站列表", "type": "docker", "enabled": true, "settings": {"sites": [{"url": "https://chat.deepseek.com/", "name": "DeepSeek Chat", "wait_time": [3, 5]}, {"url": "https://jimeng.jianying.com/ai-tool/home", "name": "剪映AI工具", "wait_time": [3, 5]}], "browser_wait_between_sites": [2, 4], "close_tabs_after_visit": true}}, "data_collection": {"id": "data_collection", "name": "流程C：数据采集", "description": "自动采集指定网站数据", "type": "scraping", "enabled": true, "settings": {"target_sites": [], "collection_interval": 300, "max_pages": 10}}}, "browser_bindings": {}, "browser_status": {}, "last_updated": null}