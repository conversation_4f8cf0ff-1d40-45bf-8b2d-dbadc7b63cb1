{"browser_data": {"last_sync": 1752633017, "total_browsers": 12, "browsers": {"12": {"profile_id": 12, "name": "gs_15219196694", "note": "gs_15219196694", "group_id": 204423, "group_name": "docker", "last_open_time": 1752632124, "proxy_type": "direct", "color": "#C71585", "tag_name": ""}, "11": {"profile_id": 11, "name": "gs_17786956691", "note": "gs_17786956691", "group_id": 204423, "group_name": "docker", "last_open_time": 1752592884, "proxy_type": "direct", "color": "#C71585", "tag_name": ""}, "10": {"profile_id": 10, "name": "gs_17357969072", "note": "gs_17357969072", "group_id": 204423, "group_name": "docker", "last_open_time": 1752592369, "proxy_type": "direct", "color": "#C71585", "tag_name": ""}, "9": {"profile_id": 9, "name": "bj_18814386012", "note": "bj_18814386012", "group_id": 204423, "group_name": "docker", "last_open_time": 1752632416, "proxy_type": "socks5", "color": "#C71585", "tag_name": ""}, "8": {"profile_id": 8, "name": "bj_13138092867", "note": "bj_13138092867", "group_id": 204423, "group_name": "docker", "last_open_time": 1752632336, "proxy_type": "socks5", "color": "#C71585", "tag_name": ""}, "7": {"profile_id": 7, "name": "长视频1", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752552554, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "6": {"profile_id": 6, "name": "故事1", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752630867, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "5": {"profile_id": 5, "name": "乡村4", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752553639, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "4": {"profile_id": 4, "name": "乡村3", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752554742, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "3": {"profile_id": 3, "name": "乡村2", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752554344, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "2": {"profile_id": 2, "name": "乡村1", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752552008, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}, "1": {"profile_id": 1, "name": "YouTube_猫咪", "note": "", "group_id": 202389, "group_name": "YouTube", "last_open_time": 1752553087, "proxy_type": "socks5", "color": "#FF0033", "tag_name": ""}}}, "youtube_automation": {"enabled_browsers": {"12": {"participate": false, "note": "分组排除 - docker"}, "11": {"participate": false, "note": "分组排除 - docker"}, "10": {"participate": false, "note": "分组排除 - docker"}, "9": {"participate": false, "note": "分组排除 - docker"}, "8": {"participate": false, "note": "分组排除 - docker"}, "7": {"participate": true, "note": "自动包含 - YouTube"}, "6": {"participate": true, "note": "自动包含 - YouTube"}, "5": {"participate": true, "note": "自动包含 - YouTube"}, "4": {"participate": true, "note": "自动包含 - YouTube"}, "3": {"participate": true, "note": "自动包含 - YouTube"}, "2": {"participate": true, "note": "自动包含 - YouTube"}, "1": {"participate": true, "note": "自动包含 - YouTube"}}, "group_settings": {"docker": {"auto_include": false, "project_type": "other"}, "YouTube": {"auto_include": true, "project_type": "youtube"}}}, "docker_automation": {"enabled_browsers": {"12": {"participate": true, "note": "自动包含 - docker"}, "11": {"participate": true, "note": "自动包含 - docker"}, "10": {"participate": true, "note": "自动包含 - docker"}, "9": {"participate": true, "note": "自动包含 - docker"}, "8": {"participate": true, "note": "自动包含 - docker"}, "7": {"participate": false, "note": "分组排除 - YouTube"}, "6": {"participate": false, "note": "分组排除 - YouTube"}, "5": {"participate": false, "note": "分组排除 - YouTube"}, "4": {"participate": false, "note": "分组排除 - YouTube"}, "3": {"participate": false, "note": "分组排除 - YouTube"}, "2": {"participate": false, "note": "分组排除 - YouTube"}, "1": {"participate": false, "note": "分组排除 - YouTube"}}, "group_settings": {"docker": {"auto_include": true, "project_type": "docker"}, "YouTube": {"auto_include": false, "project_type": "other"}}}, "docker_settings": {"sites": [{"url": "https://chat.deepseek.com/", "name": "DeepSeek Chat", "wait_time": [3, 5]}, {"url": "https://jimeng.jianying.com/ai-tool/home", "name": "剪映AI工具", "wait_time": [3, 5]}], "browser_wait_between_sites": [2, 4], "close_tabs_after_visit": true}, "settings": {"min_watch_time": 3, "max_watch_time": 8, "like_probability": 0.3, "subscribe_probability": 0.1, "scroll_count": 50}, "api": {"base_url": "http://127.0.0.1:53200", "timeout": 30}}