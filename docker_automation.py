#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker 自动化任务执行器
按照配置文件中的浏览器设置，执行 Docker 自动化任务
"""

import asyncio
import random
import logging
import sys
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.browser_api import ixBrowserAPI
from src.docker_bot import DockerBot
from utils.config_manager import ConfigManager
from utils.console_logger import ConsoleLoggerSetup


class DockerAutomation:
    """Docker 自动化任务执行器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.browser_api = ixBrowserAPI()
        self.logger = None
        self.total_stats = {
            "browsers_completed": 0,
            "total_sites_visited": 0,
            "total_successful_visits": 0,
            "total_failed_visits": 0,
            "total_errors": 0,
            "start_time": None,
            "end_time": None
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 设置控制台日志
        console_logger = ConsoleLoggerSetup()
        self.logger = console_logger.setup_logging(
            level=logging.INFO,
            log_file="logs/docker_automation.log"
        )
        
        self.logger.info("=" * 60)
        self.logger.info("Docker 自动化任务执行器启动")
        self.logger.info("=" * 60)
    
    def check_environment(self) -> bool:
        """检查运行环境"""
        print("检查运行环境...")
        
        # 检查 API 服务
        if not self.browser_api.check_api_server():
            print("❌ ixBrowser API 服务不可用")
            print("请确保 ixBrowser 软件正在运行")
            return False
        
        print("✅ ixBrowser API 服务正常")
        
        # 检查配置文件
        try:
            config = self.config_manager.load_config()
            
            # 检查 Docker 自动化配置
            if "docker_automation" in config:
                docker_browsers = self.config_manager.get_docker_enabled_browsers()
                if not docker_browsers:
                    print("❌ 没有设置为参与 Docker 自动化的浏览器")
                    print("💡 请运行 'python browser_config_sync.py' 配置 Docker 自动化")
                    return False
                
                print(f"✅ 找到 {len(docker_browsers)} 个参与 Docker 自动化的浏览器")
                return True
            else:
                print("❌ 没有找到 Docker 自动化配置")
                print("💡 请运行 'python browser_config_sync.py' 配置 Docker 自动化")
                return False
            
        except Exception as e:
            print(f"❌ 配置文件错误: {e}")
            return False
    
    def display_execution_plan(self, browsers: List[Dict[str, Any]], settings: Dict[str, Any]):
        """显示执行计划"""
        print("\n" + "=" * 60)
        print("📋 Docker 自动化执行计划")
        print("=" * 60)

        print(f"执行模式: 顺序执行")
        print(f"浏览器数量: {len(browsers)} 个")
        
        # 显示网站列表
        sites = settings.get("sites", [])
        print(f"访问网站数量: {len(sites)} 个")
        for i, site in enumerate(sites, 1):
            name = site.get("name", site.get("url", "未知"))
            wait_time = site.get("wait_time", [3, 5])
            print(f"  {i}. {name} (等待 {wait_time[0]}-{wait_time[1]} 秒)")

        print(f"\n📝 浏览器执行顺序:")
        for i, browser in enumerate(browsers, 1):
            name = browser.get("name", "未知")
            print(f"  {i}. {name} (ID: {browser.get('id', '未知')})")

        # 估算总时间
        avg_wait_per_site = sum(sum(site.get("wait_time", [3, 5])) / 2 for site in sites)
        total_time_per_browser = avg_wait_per_site + len(sites) * 2  # 加上导航时间
        estimated_minutes = (len(browsers) * total_time_per_browser) / 60

        print(f"\n⏱️ 预估总时间: {estimated_minutes:.1f} 分钟")
        print(f"🚀 自动开始执行...")
        print("=" * 60)
    
    async def run_single_browser(self, browser_data: Dict[str, Any], settings: Dict[str, Any]) -> Dict[str, int]:
        """
        运行单个浏览器的 Docker 自动化任务
        
        Args:
            browser_data: 浏览器配置数据
            settings: 运行设置
            
        Returns:
            Dict[str, int]: 运行统计数据
        """
        browser_id = browser_data["id"]
        browser_name = browser_data["name"]
        
        print(f"\n🚀 开始执行: {browser_name} (ID: {browser_id})")
        sites_count = len(settings.get("sites", []))
        print(f"📊 将访问 {sites_count} 个网站")
        self.logger.info(f"开始执行浏览器: {browser_name} (ID: {browser_id})")
        
        bot = None
        try:
            # 1. 打开浏览器
            print(f"🔗 正在打开浏览器...")
            ws_endpoint = self.browser_api.open_browser(browser_id)
            
            if not ws_endpoint:
                raise Exception("无法获取 WebSocket 端点")
            
            print(f"✅ 浏览器打开成功")
            
            # 2. 创建机器人实例
            bot = DockerBot(browser_id, ws_endpoint)
            
            # 3. 初始化浏览器连接
            print(f"🔧 正在初始化浏览器连接...")
            if not await bot.init_browser():
                raise Exception("浏览器初始化失败")
            
            print(f"✅ 浏览器初始化成功")
            
            # 4. 运行自动化任务
            print(f"🌐 开始访问网站...")
            start_time = time.time()
            
            final_stats = await bot.run(settings)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 5. 显示结果
            print(f"✅ {browser_name} 任务完成!")
            print(f"📊 统计结果:")
            print(f"   - 访问网站: {final_stats.get('sites_visited', 0)} 个")
            print(f"   - 成功访问: {final_stats.get('successful_visits', 0)} 个")
            print(f"   - 失败访问: {final_stats.get('failed_visits', 0)} 个")
            print(f"   - 总等待时间: {final_stats.get('total_wait_time', 0)} 秒")
            print(f"   - 错误次数: {final_stats.get('errors', 0)} 次")
            print(f"   - 执行时间: {duration:.1f} 秒")
            
            self.logger.info(f"浏览器 {browser_name} 任务完成 - "
                           f"访问: {final_stats.get('sites_visited', 0)}, "
                           f"成功: {final_stats.get('successful_visits', 0)}, "
                           f"时间: {duration:.1f}秒")
            
            return final_stats
            
        except Exception as e:
            error_msg = f"浏览器 {browser_name} 执行失败: {e}"
            print(f"❌ {error_msg}")
            self.logger.error(error_msg)
            
            return {
                "sites_visited": 0,
                "successful_visits": 0,
                "failed_visits": 0,
                "total_wait_time": 0,
                "errors": 1
            }
            
        finally:
            # 清理资源
            if bot:
                await bot.cleanup()
            
            # 关闭浏览器
            try:
                self.browser_api.close_browser(browser_id)
            except Exception as e:
                self.logger.error(f"关闭浏览器 {browser_id} 失败: {e}")
    
    async def run_docker_automation(self):
        """运行 Docker 自动化任务"""
        try:
            # 1. 加载配置
            config = self.config_manager.load_config()
            docker_settings = config.get("docker_settings", {})
            
            # 2. 获取参与 Docker 自动化的浏览器
            enabled_browsers = self.config_manager.get_docker_enabled_browsers()
            print(f"📊 找到 {len(enabled_browsers)} 个参与 Docker 自动化的浏览器")

            if not enabled_browsers:
                print("❌ 没有可执行的浏览器配置")
                return

            # 3. 显示浏览器详细信息
            print(f"\n📋 将要执行的浏览器:")
            for i, browser in enumerate(enabled_browsers, 1):
                name = browser.get("name", "未命名")
                group_name = browser.get("group_name", "未知分组")
                print(f"  {i}. {name} (分组: {group_name})")

            # 4. 显示执行计划
            self.display_execution_plan(enabled_browsers, docker_settings)

            # 5. 自动开始执行
            print(f"🎯 自动开始执行 {len(enabled_browsers)} 个浏览器的 Docker 自动化任务...")
            time.sleep(3)
            
            # 6. 记录开始时间
            self.total_stats["start_time"] = time.time()
            
            print(f"\n🎯 开始执行 Docker 自动化任务...")
            print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 7. 逐个执行浏览器任务
            for i, browser_data in enumerate(enabled_browsers, 1):
                print(f"\n{'='*60}")
                print(f"📍 进度: {i}/{len(enabled_browsers)}")
                print(f"{'='*60}")
                
                # 执行单个浏览器任务
                stats = await self.run_single_browser(browser_data, docker_settings)
                
                # 更新总统计
                self.total_stats["browsers_completed"] += 1
                self.total_stats["total_sites_visited"] += stats.get("sites_visited", 0)
                self.total_stats["total_successful_visits"] += stats.get("successful_visits", 0)
                self.total_stats["total_failed_visits"] += stats.get("failed_visits", 0)
                self.total_stats["total_errors"] += stats.get("errors", 0)
                
                # 如果不是最后一个浏览器，等待一段时间
                if i < len(enabled_browsers):
                    wait_time = random.randint(3, 8)
                    print(f"⏳ 等待 {wait_time} 秒后继续下一个浏览器...")
                    await asyncio.sleep(wait_time)

            # 8. 记录结束时间并显示总结
            self.total_stats["end_time"] = time.time()
            self.display_final_summary()

        except Exception as e:
            self.logger.error(f"Docker 自动化执行失败: {e}")
            print(f"❌ Docker 自动化执行失败: {e}")
    
    def display_final_summary(self):
        """显示最终总结"""
        duration = self.total_stats["end_time"] - self.total_stats["start_time"]
        
        print(f"\n{'='*60}")
        print("🎉 Docker 自动化任务全部完成!")
        print(f"{'='*60}")
        print(f"📊 总体统计:")
        print(f"   - 完成浏览器: {self.total_stats['browsers_completed']} 个")
        print(f"   - 总访问网站: {self.total_stats['total_sites_visited']} 次")
        print(f"   - 成功访问: {self.total_stats['total_successful_visits']} 次")
        print(f"   - 失败访问: {self.total_stats['total_failed_visits']} 次")
        print(f"   - 总错误数: {self.total_stats['total_errors']} 次")
        print(f"   - 总执行时间: {duration/60:.1f} 分钟")
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.logger.info(f"Docker 自动化任务全部完成 - "
                        f"浏览器: {self.total_stats['browsers_completed']}, "
                        f"访问: {self.total_stats['total_sites_visited']}, "
                        f"成功: {self.total_stats['total_successful_visits']}, "
                        f"时间: {duration/60:.1f}分钟")


async def main():
    """主函数"""
    automation = DockerAutomation()
    
    print("🐳 Docker 自动化任务执行器")
    print("=" * 50)
    
    # 检查环境
    if not automation.check_environment():
        print("\n❌ 环境检查失败，程序退出")
        return
    
    # 运行自动化
    await automation.run_docker_automation()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序运行时发生未知错误: {e}")
        logging.error(f"程序运行时发生未知错误: {e}")
