#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器流程配置应用程序
专门用于配置浏览器与自动化流程的绑定关系
"""

import sys
import os
import logging
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, 
    QMenuBar, QMenu, QMessageBox, QStatusBar
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QAction

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.gui.browser_flow_config_panel import BrowserFlowConfigPanel

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FlowConfigMainWindow(QMainWindow):
    """浏览器流程配置主窗口"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self.setup_window()
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        
        logger.info("浏览器流程配置窗口初始化完成")
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("浏览器自动化流程配置工具 v1.0")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
        """)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建配置面板
        self.config_panel = BrowserFlowConfigPanel()
        main_layout.addWidget(self.config_panel)
        
        # 连接信号
        self.config_panel.configuration_changed.connect(self.on_configuration_changed)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        # 刷新数据
        refresh_action = QAction("刷新数据", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_data)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具")
        
        # 配置摘要
        summary_action = QAction("配置摘要", self)
        summary_action.triggered.connect(self.show_configuration_summary)
        tools_menu.addAction(summary_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪 - 浏览器流程配置工具")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            self.config_panel.refresh_data()
            self.status_bar.showMessage("数据刷新完成", 3000)
            logger.info("数据刷新完成")
        except Exception as e:
            logger.error(f"刷新数据失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新数据失败: {e}")
    
    def on_configuration_changed(self):
        """配置变化处理"""
        self.status_bar.showMessage("配置已更新", 2000)
        logger.info("配置已更新")
    
    def show_configuration_summary(self):
        """显示配置摘要"""
        try:
            summary = self.config_panel.get_configuration_summary()
            
            message = f"""配置摘要信息：

总浏览器数量: {summary.get('total_browsers', 0)}
已配置流程: {summary.get('bound_browsers', 0)}
未配置流程: {summary.get('unbound_browsers', 0)}
可用流程数: {summary.get('available_flows', 0)}

流程分布统计:"""
            
            flow_stats = summary.get('flow_statistics', {})
            for flow_id, count in flow_stats.items():
                message += f"\n  - {flow_id}: {count} 个浏览器"
            
            QMessageBox.information(self, "配置摘要", message)
            
        except Exception as e:
            logger.error(f"获取配置摘要失败: {e}")
            QMessageBox.warning(self, "警告", f"获取配置摘要失败: {e}")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于", 
            "浏览器自动化流程配置工具 v1.0\n\n"
            "专门用于管理浏览器配置文件与自动化流程的绑定关系\n\n"
            "功能特性：\n"
            "• 直观的两栏布局界面\n"
            "• 支持多种自动化流程类型\n"
            "• 实时配置保存和加载\n"
            "• 完整的配置管理功能\n\n"
            "技术栈：Python, PyQt6, JSON配置"
        )
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        reply = QMessageBox.question(
            self, "确认退出", 
            "确定要退出浏览器流程配置工具吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            logger.info("用户退出应用程序")
            event.accept()
        else:
            event.ignore()


def check_dependencies():
    """检查必要的依赖"""
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
    except ImportError:
        print("❌ PyQt6 未安装，请运行: pip install PyQt6")
        return False
    
    try:
        from src.gui.browser_flow_config_panel import BrowserFlowConfigPanel
        print("✅ 配置面板模块导入成功")
    except ImportError as e:
        print(f"❌ 配置面板模块导入失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🚀 启动浏览器流程配置工具")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的依赖包")
        input("按回车键退出...")
        return
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("浏览器流程配置工具")
        app.setApplicationVersion("1.0")
        
        # 设置高DPI支持 (PyQt6兼容)
        try:
            app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
            app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        except AttributeError:
            # PyQt6中这些属性可能不存在或已默认启用
            pass
        
        # 创建主窗口
        main_window = FlowConfigMainWindow()
        main_window.show()
        
        print("✅ 应用程序启动成功")
        print("💡 关闭主窗口以退出程序")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有必要的模块都已正确安装")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        logging.exception("应用程序启动时发生异常")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
