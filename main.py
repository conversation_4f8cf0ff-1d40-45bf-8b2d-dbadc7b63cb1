#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ixBrowser YouTube 自动化工具主启动脚本
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ['PYTHONPATH'] = str(project_root)

def check_dependencies():
    """检查必要的依赖"""
    try:
        import PyQt6
        print("✅ PyQt6 已安装")
    except ImportError:
        print("❌ PyQt6 未安装，请运行: pip install PyQt6")
        return False
    
    try:
        from src.main_window import MainWindow
        print("✅ 主窗口模块导入成功")
    except ImportError as e:
        print(f"❌ 主窗口模块导入失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动 ixBrowser YouTube 自动化工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的依赖包")
        input("按回车键退出...")
        return
    
    try:
        # 导入 PyQt6 应用程序
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from src.main_window import MainWindow
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("ixBrowser YouTube 自动化工具")
        app.setApplicationVersion("1.0")
        
        # 设置高DPI支持
        app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()
        
        print("✅ 应用程序启动成功")
        print("💡 关闭主窗口以退出程序")
        
        # 运行应用程序
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保所有必要的模块都已正确安装")
        input("按回车键退出...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        logging.exception("应用程序启动时发生异常")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
