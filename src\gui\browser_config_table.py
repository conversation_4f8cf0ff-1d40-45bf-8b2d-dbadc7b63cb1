"""
浏览器配置表格组件
用于展示浏览器列表和当前绑定的自动化流程
"""

import logging
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)


class BrowserConfigTable(QTableWidget):
    """浏览器配置表格"""
    
    # 定义信号
    browser_selected = pyqtSignal(str)  # 浏览器被选中时发出信号，传递浏览器ID
    
    def __init__(self, parent=None):
        """
        初始化表格
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.browser_data = []
        self.setup_table()
        self.connect_signals()
    
    def setup_table(self):
        """设置表格属性"""
        # 设置列数和列标题
        self.setColumnCount(4)
        headers = ["配置文件名称", "浏览器ID", "分组", "当前绑定流程"]
        self.setHorizontalHeaderLabels(headers)
        
        # 设置表格属性
        self.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        
        # 设置列宽
        header = self.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # 配置文件名称列自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # ID列适应内容
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 分组列适应内容
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # 流程列自适应
        
        # 设置表格样式
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f5f5f5;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 8px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """)
        
        # 设置字体
        font = QFont()
        font.setPointSize(9)
        self.setFont(font)
    
    def connect_signals(self):
        """连接信号"""
        self.itemSelectionChanged.connect(self.on_selection_changed)
    
    def load_browser_data(self, browsers: List[Dict[str, Any]]):
        """
        加载浏览器数据到表格
        
        Args:
            browsers: 浏览器数据列表
        """
        self.browser_data = browsers
        self.setRowCount(len(browsers))
        
        for row, browser in enumerate(browsers):
            # 配置文件名称
            name_item = QTableWidgetItem(browser.get("name", "未命名"))
            name_item.setFlags(name_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.setItem(row, 0, name_item)
            
            # 浏览器ID
            id_item = QTableWidgetItem(browser.get("id", ""))
            id_item.setFlags(id_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.setItem(row, 1, id_item)
            
            # 分组
            group_item = QTableWidgetItem(browser.get("group_name", ""))
            group_item.setFlags(group_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.setItem(row, 2, group_item)
            
            # 当前绑定流程
            flow_item = QTableWidgetItem(browser.get("bound_flow_name", "未配置"))
            flow_item.setFlags(flow_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.setItem(row, 3, flow_item)
        
        logger.info(f"加载了 {len(browsers)} 个浏览器配置到表格")
    
    def update_browser_flow(self, browser_id: str, flow_name: str):
        """
        更新指定浏览器的流程绑定显示
        
        Args:
            browser_id: 浏览器ID
            flow_name: 新的流程名称
        """
        for row in range(self.rowCount()):
            id_item = self.item(row, 1)
            if id_item and id_item.text() == browser_id:
                flow_item = self.item(row, 3)
                if flow_item:
                    flow_item.setText(flow_name)
                    
                # 更新内部数据
                for browser in self.browser_data:
                    if browser.get("id") == browser_id:
                        browser["bound_flow_name"] = flow_name
                        break
                
                logger.info(f"更新浏览器 {browser_id} 的流程绑定显示为: {flow_name}")
                break
    
    def get_selected_browser_id(self) -> Optional[str]:
        """
        获取当前选中的浏览器ID
        
        Returns:
            Optional[str]: 选中的浏览器ID，未选中时返回None
        """
        current_row = self.currentRow()
        if current_row >= 0:
            id_item = self.item(current_row, 1)
            if id_item:
                return id_item.text()
        return None
    
    def get_selected_browser_data(self) -> Optional[Dict[str, Any]]:
        """
        获取当前选中的浏览器完整数据
        
        Returns:
            Optional[Dict[str, Any]]: 选中的浏览器数据，未选中时返回None
        """
        browser_id = self.get_selected_browser_id()
        if browser_id:
            for browser in self.browser_data:
                if browser.get("id") == browser_id:
                    return browser
        return None
    
    def refresh_data(self, browsers: List[Dict[str, Any]]):
        """
        刷新表格数据
        
        Args:
            browsers: 新的浏览器数据列表
        """
        # 保存当前选中的浏览器ID
        selected_id = self.get_selected_browser_id()
        
        # 重新加载数据
        self.load_browser_data(browsers)
        
        # 尝试恢复选中状态
        if selected_id:
            self.select_browser_by_id(selected_id)
    
    def select_browser_by_id(self, browser_id: str):
        """
        根据浏览器ID选中对应行
        
        Args:
            browser_id: 要选中的浏览器ID
        """
        for row in range(self.rowCount()):
            id_item = self.item(row, 1)
            if id_item and id_item.text() == browser_id:
                self.selectRow(row)
                break
    
    def on_selection_changed(self):
        """处理选择变化事件"""
        browser_id = self.get_selected_browser_id()
        if browser_id:
            self.browser_selected.emit(browser_id)
            logger.debug(f"选中浏览器: {browser_id}")
    
    def clear_selection_signal(self):
        """清除选择但不发出信号"""
        self.blockSignals(True)
        self.clearSelection()
        self.blockSignals(False)
