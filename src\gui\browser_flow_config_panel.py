"""
浏览器流程配置面板
整合浏览器表格和流程选择面板的主界面
"""

import logging
from typing import List, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QSplitter, 
    QPushButton, QMessageBox, QLabel
)
from PyQt6.QtCore import Qt, pyqtSignal

from src.gui.browser_config_table import BrowserConfigTable
from src.gui.flow_selection_panel import FlowSelectionPanel
from utils.automation_flow_manager import AutomationFlowManager

logger = logging.getLogger(__name__)


class BrowserFlowConfigPanel(QWidget):
    """浏览器流程配置面板"""
    
    # 定义信号
    configuration_changed = pyqtSignal()  # 配置发生变化时发出信号
    
    def __init__(self, parent=None):
        """
        初始化面板
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.flow_manager = AutomationFlowManager()
        self.setup_ui()
        self.connect_signals()
        self.load_initial_data()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 标题区域
        title_layout = QHBoxLayout()
        
        title_label = QLabel("浏览器自动化流程配置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                padding: 10px;
            }
        """)
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.setMinimumHeight(35)
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 11px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        title_layout.addWidget(self.refresh_button)
        
        layout.addLayout(title_layout)
        
        # 主要内容区域 - 使用分割器实现两栏布局
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：浏览器表格 (70%)
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 5, 0)
        
        # 浏览器表格标题
        table_label = QLabel("浏览器列表")
        table_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                font-weight: bold;
                color: #555;
                padding: 5px 0;
            }
        """)
        left_layout.addWidget(table_label)
        
        # 浏览器配置表格
        self.browser_table = BrowserConfigTable()
        left_layout.addWidget(self.browser_table)
        
        splitter.addWidget(left_widget)
        
        # 右侧：流程选择面板 (30%)
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(5, 0, 0, 0)
        
        # 流程选择面板
        self.flow_panel = FlowSelectionPanel()
        right_layout.addWidget(self.flow_panel)
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例 (70% : 30%)
        splitter.setSizes([700, 300])
        splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠
        
        layout.addWidget(splitter)
        
        # 底部状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 10px;
                padding: 5px;
                border-top: 1px solid #e0e0e0;
            }
        """)
        layout.addWidget(self.status_label)
    
    def connect_signals(self):
        """连接信号"""
        # 浏览器表格选择信号
        self.browser_table.browser_selected.connect(self.on_browser_selected)
        
        # 流程保存信号
        self.flow_panel.flow_saved.connect(self.on_flow_saved)
        
        # 刷新按钮
        self.refresh_button.clicked.connect(self.refresh_data)
    
    def load_initial_data(self):
        """加载初始数据"""
        try:
            # 加载可用流程
            available_flows = self.flow_manager.get_available_flows()
            self.flow_panel.set_available_flows(available_flows)
            
            # 加载浏览器数据
            browsers = self.flow_manager.get_browsers_with_flows()
            self.browser_table.load_browser_data(browsers)
            
            self.status_label.setText(f"已加载 {len(browsers)} 个浏览器配置，{len(available_flows)} 个可用流程")
            logger.info("初始数据加载完成")
            
        except Exception as e:
            logger.error(f"加载初始数据失败: {e}")
            QMessageBox.critical(self, "错误", f"加载数据失败: {e}")
            self.status_label.setText("数据加载失败")
    
    def refresh_data(self):
        """刷新数据"""
        try:
            self.status_label.setText("正在刷新数据...")
            
            # 重新加载可用流程
            available_flows = self.flow_manager.get_available_flows()
            self.flow_panel.set_available_flows(available_flows)
            
            # 重新加载浏览器数据
            browsers = self.flow_manager.get_browsers_with_flows()
            self.browser_table.refresh_data(browsers)
            
            self.status_label.setText(f"刷新完成 - {len(browsers)} 个浏览器配置，{len(available_flows)} 个可用流程")
            logger.info("数据刷新完成")
            
        except Exception as e:
            logger.error(f"刷新数据失败: {e}")
            QMessageBox.critical(self, "错误", f"刷新数据失败: {e}")
            self.status_label.setText("数据刷新失败")
    
    def on_browser_selected(self, browser_id: str):
        """
        处理浏览器选择事件
        
        Args:
            browser_id: 选中的浏览器ID
        """
        try:
            browser_data = self.browser_table.get_selected_browser_data()
            if browser_data:
                browser_name = browser_data.get("name", "未命名")
                current_flow_id = browser_data.get("bound_flow_id", "none")
                
                # 设置流程选择面板的当前浏览器
                self.flow_panel.set_current_browser(browser_id, browser_name, current_flow_id)
                
                self.status_label.setText(f"已选择浏览器: {browser_name} (ID: {browser_id})")
                logger.debug(f"选择浏览器: {browser_name} (ID: {browser_id})")
            
        except Exception as e:
            logger.error(f"处理浏览器选择失败: {e}")
            QMessageBox.warning(self, "警告", f"处理浏览器选择失败: {e}")
    
    def on_flow_saved(self, browser_id: str, flow_id: str, flow_name: str):
        """
        处理流程保存事件
        
        Args:
            browser_id: 浏览器ID
            flow_id: 流程ID
            flow_name: 流程名称
        """
        try:
            # 保存到配置文件
            if self.flow_manager.set_browser_flow_binding(browser_id, flow_id):
                # 更新表格显示
                self.browser_table.update_browser_flow(browser_id, flow_name)
                
                # 发出配置变化信号
                self.configuration_changed.emit()
                
                self.status_label.setText(f"已保存配置: 浏览器 {browser_id} -> 流程 {flow_name}")
                logger.info(f"保存配置成功: 浏览器 {browser_id} -> 流程 {flow_id}")
            else:
                QMessageBox.critical(self, "错误", "保存配置失败")
                self.status_label.setText("保存配置失败")
                
        except Exception as e:
            logger.error(f"保存流程配置失败: {e}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {e}")
            self.status_label.setText("保存配置失败")
    
    def get_configuration_summary(self) -> Dict[str, Any]:
        """
        获取配置摘要信息
        
        Returns:
            Dict[str, Any]: 配置摘要
        """
        try:
            browsers = self.flow_manager.get_browsers_with_flows()
            flows = self.flow_manager.get_available_flows()
            
            # 统计绑定情况
            bound_count = 0
            unbound_count = 0
            flow_stats = {}
            
            for browser in browsers:
                flow_id = browser.get("bound_flow_id", "none")
                if flow_id == "none":
                    unbound_count += 1
                else:
                    bound_count += 1
                    flow_stats[flow_id] = flow_stats.get(flow_id, 0) + 1
            
            return {
                "total_browsers": len(browsers),
                "bound_browsers": bound_count,
                "unbound_browsers": unbound_count,
                "available_flows": len(flows),
                "flow_statistics": flow_stats
            }
            
        except Exception as e:
            logger.error(f"获取配置摘要失败: {e}")
            return {}
