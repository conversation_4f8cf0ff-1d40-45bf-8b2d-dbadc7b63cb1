"""
流程选择面板组件
用于选择和配置浏览器的自动化流程
"""

import logging
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
    QPushButton, QTextEdit, QGroupBox, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

logger = logging.getLogger(__name__)


class FlowSelectionPanel(QWidget):
    """流程选择面板"""
    
    # 定义信号
    flow_saved = pyqtSignal(str, str, str)  # 流程保存成功时发出信号：browser_id, flow_id, flow_name
    
    def __init__(self, parent=None):
        """
        初始化面板
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.current_browser_id = None
        self.current_browser_name = None
        self.available_flows = []
        self.setup_ui()
        self.connect_signals()
        self.set_enabled(False)  # 初始状态禁用
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 创建分组框
        group_box = QGroupBox("自动化流程配置")
        group_layout = QVBoxLayout(group_box)
        
        # 当前选中的浏览器标题
        self.browser_title = QLabel("请选择一个浏览器")
        self.browser_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #333;
                padding: 8px;
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            }
        """)
        group_layout.addWidget(self.browser_title)
        
        # 流程选择区域
        flow_layout = QVBoxLayout()
        
        # 流程选择标签
        flow_label = QLabel("自动化流程:")
        flow_label.setFont(QFont("", 10, QFont.Weight.Bold))
        flow_layout.addWidget(flow_label)
        
        # 流程下拉选择框
        self.flow_combo = QComboBox()
        self.flow_combo.setMinimumHeight(35)
        self.flow_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
                font-size: 11px;
            }
            QComboBox:focus {
                border-color: #0078d4;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
        """)
        flow_layout.addWidget(self.flow_combo)
        
        group_layout.addLayout(flow_layout)
        
        # 流程描述区域
        desc_label = QLabel("流程描述:")
        desc_label.setFont(QFont("", 10, QFont.Weight.Bold))
        group_layout.addWidget(desc_label)
        
        self.flow_description = QTextEdit()
        self.flow_description.setMaximumHeight(80)
        self.flow_description.setReadOnly(True)
        self.flow_description.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: #f9f9f9;
                padding: 8px;
                font-size: 10px;
                color: #666;
            }
        """)
        group_layout.addWidget(self.flow_description)
        
        # 保存按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.save_button = QPushButton("保存配置")
        self.save_button.setMinimumHeight(40)
        self.save_button.setMinimumWidth(120)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
                padding: 10px 20px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.save_button)
        
        group_layout.addLayout(button_layout)
        
        layout.addWidget(group_box)
        layout.addStretch()
    
    def connect_signals(self):
        """连接信号"""
        self.flow_combo.currentTextChanged.connect(self.on_flow_changed)
        self.save_button.clicked.connect(self.save_configuration)
    
    def set_available_flows(self, flows: List[Dict[str, Any]]):
        """
        设置可用的流程列表
        
        Args:
            flows: 可用流程列表
        """
        self.available_flows = flows
        self.flow_combo.clear()
        
        for flow in flows:
            self.flow_combo.addItem(flow.get("name", "未命名流程"), flow.get("id"))
        
        logger.info(f"设置了 {len(flows)} 个可用流程")
    
    def set_current_browser(self, browser_id: str, browser_name: str, current_flow_id: Optional[str] = None):
        """
        设置当前选中的浏览器
        
        Args:
            browser_id: 浏览器ID
            browser_name: 浏览器名称
            current_flow_id: 当前绑定的流程ID
        """
        self.current_browser_id = browser_id
        self.current_browser_name = browser_name
        
        # 更新标题
        self.browser_title.setText(f"当前选中: {browser_name}")
        
        # 设置当前流程选择
        if current_flow_id:
            index = self.flow_combo.findData(current_flow_id)
            if index >= 0:
                self.flow_combo.setCurrentIndex(index)
            else:
                # 如果找不到对应的流程，默认选择"无选择"
                none_index = self.flow_combo.findData("none")
                if none_index >= 0:
                    self.flow_combo.setCurrentIndex(none_index)
        else:
            # 默认选择"无选择"
            none_index = self.flow_combo.findData("none")
            if none_index >= 0:
                self.flow_combo.setCurrentIndex(none_index)
        
        # 启用面板
        self.set_enabled(True)
        
        logger.info(f"设置当前浏览器: {browser_name} (ID: {browser_id})")
    
    def clear_current_browser(self):
        """清除当前选中的浏览器"""
        self.current_browser_id = None
        self.current_browser_name = None
        self.browser_title.setText("请选择一个浏览器")
        self.flow_description.clear()
        self.set_enabled(False)
    
    def set_enabled(self, enabled: bool):
        """
        设置面板启用状态
        
        Args:
            enabled: 是否启用
        """
        self.flow_combo.setEnabled(enabled)
        self.save_button.setEnabled(enabled)
    
    def on_flow_changed(self):
        """处理流程选择变化"""
        current_flow_id = self.flow_combo.currentData()
        if current_flow_id:
            # 查找对应的流程描述
            for flow in self.available_flows:
                if flow.get("id") == current_flow_id:
                    description = flow.get("description", "无描述")
                    self.flow_description.setText(description)
                    break
            else:
                self.flow_description.clear()
    
    def save_configuration(self):
        """保存配置"""
        if not self.current_browser_id:
            QMessageBox.warning(self, "警告", "请先选择一个浏览器")
            return
        
        current_flow_id = self.flow_combo.currentData()
        current_flow_name = self.flow_combo.currentText()
        
        if not current_flow_id:
            QMessageBox.warning(self, "警告", "请选择一个自动化流程")
            return
        
        # 发出保存信号
        self.flow_saved.emit(self.current_browser_id, current_flow_id, current_flow_name)
        
        # 显示保存成功消息
        QMessageBox.information(
            self, "保存成功", 
            f"已成功为浏览器 '{self.current_browser_name}' 配置流程 '{current_flow_name}'"
        )
        
        logger.info(f"保存配置: 浏览器 {self.current_browser_id} -> 流程 {current_flow_id}")
    
    def get_current_flow_id(self) -> Optional[str]:
        """
        获取当前选中的流程ID
        
        Returns:
            Optional[str]: 当前选中的流程ID
        """
        return self.flow_combo.currentData()
    
    def get_current_flow_name(self) -> Optional[str]:
        """
        获取当前选中的流程名称
        
        Returns:
            Optional[str]: 当前选中的流程名称
        """
        return self.flow_combo.currentText()
