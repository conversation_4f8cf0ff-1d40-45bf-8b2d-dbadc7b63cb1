"""
主窗口类
YouTube Shorts 机器人应用程序的主界面
"""

import logging
import sys
from typing import Dict, Any, List
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QMessageBox, QStatusBar, QMenuBar,
    QMenu, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QIcon

from src.browser_api import ixBrowserAPI
from src.bot_worker import BotManager
from src.gui.browser_panel import BrowserPanel
from src.gui.config_panel import ConfigPanel
from src.gui.status_panel import StatusPanel
from src.gui.log_panel import LogPanel
from src.gui.browser_flow_config_panel import BrowserFlowConfigPanel
from utils.config_manager import ConfigManager
from utils.logger_setup import logger_setup

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.browser_api = ixBrowserAPI()
        self.bot_manager = BotManager()
        
        # 运行状态
        self.is_running = False
        self.active_workers = {}
        
        # 设置窗口
        self.setup_window()
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.connect_signals()
        
        # 初始化日志系统
        self.setup_logging()
        
        # 检查 API 服务
        self.check_api_service()
        
        logger.info("主窗口初始化完成")
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("YouTube Shorts 机器人 v1.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("icon.png"))
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建控制按钮区域
        self.create_control_buttons(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 800])
    
    def create_control_buttons(self, layout):
        """创建控制按钮"""
        button_frame = QFrame()
        button_layout = QHBoxLayout(button_frame)
        
        # 开始任务按钮
        self.start_button = QPushButton("开始任务")
        self.start_button.setMinimumHeight(40)
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.start_button.clicked.connect(self.start_tasks)
        button_layout.addWidget(self.start_button)
        
        # 停止任务按钮
        self.stop_button = QPushButton("停止任务")
        self.stop_button.setMinimumHeight(40)
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.stop_button.clicked.connect(self.stop_tasks)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        # 弹性空间
        button_layout.addStretch()
        
        # API 状态指示器
        self.api_status_label = QPushButton("API 状态: 检查中...")
        self.api_status_label.setEnabled(False)
        self.api_status_label.setMinimumHeight(40)
        button_layout.addWidget(self.api_status_label)
        
        layout.addWidget(button_frame)
    
    def create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 浏览器管理面板
        self.browser_panel = BrowserPanel()
        left_layout.addWidget(self.browser_panel)
        
        # 配置面板
        self.config_panel = ConfigPanel()
        left_layout.addWidget(self.config_panel)
        
        return left_widget
    
    def create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 状态监控面板
        self.status_panel = StatusPanel()
        right_layout.addWidget(self.status_panel)
        
        # 日志面板
        self.log_panel = LogPanel()
        right_layout.addWidget(self.log_panel)
        
        return right_widget
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        # 导入配置
        import_action = QAction("导入配置", self)
        import_action.triggered.connect(self.import_config)
        file_menu.addAction(import_action)
        
        # 导出配置
        export_action = QAction("导出配置", self)
        export_action.triggered.connect(self.export_config)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具")

        # 浏览器流程配置
        flow_config_action = QAction("浏览器流程配置", self)
        flow_config_action.triggered.connect(self.open_flow_config)
        tools_menu.addAction(flow_config_action)

        tools_menu.addSeparator()

        # 检查 API
        check_api_action = QAction("检查 API 服务", self)
        check_api_action.triggered.connect(self.check_api_service)
        tools_menu.addAction(check_api_action)

        # 清除日志
        clear_logs_action = QAction("清除日志", self)
        clear_logs_action.triggered.connect(self.log_panel.clear_logs)
        tools_menu.addAction(clear_logs_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        
        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def connect_signals(self):
        """连接信号"""
        # 浏览器面板信号
        self.browser_panel.browsers_changed.connect(self.on_browsers_changed)
        
        # 配置面板信号
        self.config_panel.settings_changed.connect(self.on_settings_changed)
        
        # 状态面板信号
        self.status_panel.clear_requested.connect(self.on_status_clear_requested)
        
        # 日志面板信号
        self.log_panel.log_level_changed.connect(self.on_log_level_changed)
    
    def setup_logging(self):
        """设置日志系统"""
        # 设置日志处理器
        qt_handler = logger_setup.setup_logging(
            level=logging.INFO,
            text_edit=self.log_panel.log_text
        )
        
        # 将处理器传递给日志面板
        self.log_panel.set_log_handler(qt_handler)
    
    def check_api_service(self):
        """检查 API 服务状态"""
        try:
            if self.browser_api.check_api_server():
                self.api_status_label.setText("API 状态: 正常")
                self.api_status_label.setStyleSheet("background-color: #4CAF50; color: white;")
                self.status_bar.showMessage("ixBrowser API 服务正常")
                logger.info("ixBrowser API 服务连接正常")
            else:
                self.api_status_label.setText("API 状态: 离线")
                self.api_status_label.setStyleSheet("background-color: #f44336; color: white;")
                self.status_bar.showMessage("ixBrowser API 服务不可用")
                logger.warning("ixBrowser API 服务不可用")
                
        except Exception as e:
            self.api_status_label.setText("API 状态: 错误")
            self.api_status_label.setStyleSheet("background-color: #ff9800; color: white;")
            self.status_bar.showMessage(f"API 检查失败: {e}")
            logger.error(f"API 服务检查失败: {e}")
    
    def start_tasks(self):
        """开始任务"""
        try:
            # 检查 API 服务
            if not self.browser_api.check_api_server():
                QMessageBox.critical(
                    self, "错误", 
                    "ixBrowser API 服务不可用，请确保 ixBrowser 正在运行。"
                )
                return
            
            # 获取启用的浏览器
            enabled_browsers = self.browser_panel.get_enabled_browsers()
            if not enabled_browsers:
                QMessageBox.warning(
                    self, "警告", 
                    "请至少选择一个浏览器。"
                )
                return
            
            # 获取配置
            settings = self.config_panel.get_current_settings()
            
            # 验证配置
            if not self.config_panel.is_valid_configuration():
                errors = self.config_panel.get_validation_errors()
                QMessageBox.warning(
                    self, "配置错误", 
                    "配置验证失败：\n" + "\n".join(errors)
                )
                return
            
            # 开始监控
            self.status_panel.start_monitoring()
            
            # 启动机器人
            success_count = 0
            for browser_data in enabled_browsers:
                browser_id = browser_data["id"]
                
                # 打开浏览器
                ws_endpoint = self.browser_api.open_browser(browser_id)
                if ws_endpoint:
                    # 创建工作线程
                    worker = self.bot_manager.start_bot(browser_id, ws_endpoint, settings)
                    
                    # 连接信号
                    worker.status_updated.connect(self.status_panel.update_bot_status)
                    worker.error_occurred.connect(self.on_bot_error)
                    worker.finished_signal.connect(self.on_bot_finished)
                    
                    self.active_workers[browser_id] = worker
                    success_count += 1
                else:
                    logger.error(f"无法打开浏览器: {browser_id}")
            
            if success_count > 0:
                self.is_running = True
                self.start_button.setEnabled(False)
                self.stop_button.setEnabled(True)
                self.status_bar.showMessage(f"任务已启动，{success_count} 个机器人正在运行")
                logger.info(f"任务启动成功，{success_count} 个机器人开始运行")
            else:
                QMessageBox.critical(self, "错误", "无法启动任何机器人")
                
        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            QMessageBox.critical(self, "错误", f"启动任务失败: {e}")
    
    def stop_tasks(self):
        """停止任务"""
        try:
            # 停止所有机器人
            self.bot_manager.stop_all_bots()
            
            # 关闭浏览器
            for browser_id in self.active_workers.keys():
                self.browser_api.close_browser(browser_id)
            
            # 清理
            self.active_workers.clear()
            
            # 停止监控
            self.status_panel.stop_monitoring()
            
            # 更新 UI
            self.is_running = False
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_bar.showMessage("任务已停止")
            
            logger.info("所有任务已停止")
            
        except Exception as e:
            logger.error(f"停止任务失败: {e}")
            QMessageBox.critical(self, "错误", f"停止任务失败: {e}")
    
    def on_browsers_changed(self):
        """浏览器配置改变处理"""
        logger.info("浏览器配置已更新")
    
    def on_settings_changed(self, settings: Dict[str, Any]):
        """设置改变处理"""
        logger.info("运行设置已更新")
    
    def on_status_clear_requested(self):
        """状态清除请求处理"""
        logger.info("状态显示已清除")
    
    def on_log_level_changed(self, level: int):
        """日志级别改变处理"""
        logger_setup.set_log_level(level)
    
    def on_bot_error(self, browser_id: str, error_message: str):
        """机器人错误处理"""
        logger.error(f"机器人 {browser_id} 发生错误: {error_message}")
        self.status_panel.update_bot_status(browser_id, f"错误: {error_message}", {})
    
    def on_bot_finished(self, browser_id: str, final_stats: Dict[str, int]):
        """机器人完成处理"""
        logger.info(f"机器人 {browser_id} 已完成，统计: {final_stats}")
        
        # 从活跃列表中移除
        if browser_id in self.active_workers:
            del self.active_workers[browser_id]
        
        # 关闭浏览器
        self.browser_api.close_browser(browser_id)
        
        # 检查是否所有机器人都完成了
        if not self.active_workers and self.is_running:
            self.stop_tasks()
            QMessageBox.information(self, "完成", "所有机器人任务已完成！")
    
    def import_config(self):
        """导入配置"""
        # TODO: 实现配置导入功能
        QMessageBox.information(self, "提示", "配置导入功能待实现")
    
    def export_config(self):
        """导出配置"""
        # TODO: 实现配置导出功能
        QMessageBox.information(self, "提示", "配置导出功能待实现")
    
    def open_flow_config(self):
        """打开浏览器流程配置窗口"""
        try:
            from PyQt6.QtWidgets import QDialog, QVBoxLayout

            # 创建配置对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("浏览器自动化流程配置")
            dialog.setModal(True)
            dialog.resize(1000, 700)

            # 设置布局
            layout = QVBoxLayout(dialog)

            # 创建配置面板
            config_panel = BrowserFlowConfigPanel(dialog)
            layout.addWidget(config_panel)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            logger.error(f"打开流程配置窗口失败: {e}")
            QMessageBox.critical(self, "错误", f"打开配置窗口失败: {e}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于",
            "YouTube Shorts 机器人 v1.0\n\n"
            "一个基于 PyQt6 和 Playwright 的自动化工具\n"
            "用于在 ixBrowser 环境中自动执行 YouTube Shorts 交互\n\n"
            "功能特性：\n"
            "• 多浏览器并发运行\n"
            "• 可配置的观看、点赞、订阅行为\n"
            "• 实时状态监控\n"
            "• 详细日志记录\n\n"
            "技术栈：Python, PyQt6, Playwright, ixBrowser API"
        )
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_running:
            reply = QMessageBox.question(
                self, "确认退出", 
                "任务正在运行中，确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.stop_tasks()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
