#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试浏览器流程配置功能
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.automation_flow_manager import AutomationFlowManager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_flow_manager():
    """测试流程管理器功能"""
    print("🧪 测试浏览器流程配置功能")
    print("=" * 50)
    
    # 创建流程管理器
    flow_manager = AutomationFlowManager()
    
    # 测试1: 加载可用流程
    print("\n📋 测试1: 加载可用流程")
    flows = flow_manager.get_available_flows()
    print(f"可用流程数量: {len(flows)}")
    for flow in flows:
        print(f"  - {flow['name']} (ID: {flow['id']})")
    
    # 测试2: 获取浏览器列表
    print("\n🖥️  测试2: 获取浏览器列表")
    browsers = flow_manager.get_browsers_with_flows()
    print(f"浏览器数量: {len(browsers)}")
    for browser in browsers[:5]:  # 只显示前5个
        print(f"  - {browser['name']} (ID: {browser['id']}) -> {browser['bound_flow_name']}")
    
    if len(browsers) > 5:
        print(f"  ... 还有 {len(browsers) - 5} 个浏览器")
    
    # 测试3: 设置流程绑定
    if browsers:
        test_browser = browsers[0]
        browser_id = test_browser['id']
        
        print(f"\n🔧 测试3: 设置流程绑定")
        print(f"测试浏览器: {test_browser['name']} (ID: {browser_id})")
        
        # 绑定到 YouTube 流程
        if flow_manager.set_browser_flow_binding(browser_id, "youtube_shorts"):
            print("✅ 成功绑定到 YouTube Shorts 流程")
            
            # 验证绑定
            bound_flow = flow_manager.get_browser_flow_binding(browser_id)
            print(f"当前绑定流程: {bound_flow}")
        else:
            print("❌ 绑定失败")
        
        # 测试4: 移除绑定
        print(f"\n🗑️  测试4: 移除流程绑定")
        if flow_manager.remove_browser_flow_binding(browser_id):
            print("✅ 成功移除绑定")
            
            # 验证移除
            bound_flow = flow_manager.get_browser_flow_binding(browser_id)
            print(f"当前绑定流程: {bound_flow}")
        else:
            print("❌ 移除失败")
    
    print("\n🎉 测试完成!")


def test_gui_components():
    """测试GUI组件"""
    print("\n🖼️  测试GUI组件")
    print("=" * 30)
    
    try:
        from PyQt6.QtWidgets import QApplication
        from src.gui.browser_flow_config_panel import BrowserFlowConfigPanel
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建配置面板
        panel = BrowserFlowConfigPanel()
        panel.setWindowTitle("浏览器流程配置测试")
        panel.resize(1000, 700)
        panel.show()
        
        print("✅ GUI组件创建成功")
        print("💡 关闭窗口以继续...")
        
        # 运行应用程序
        app.exec()
        
    except ImportError as e:
        print(f"❌ GUI测试跳过 (缺少依赖): {e}")
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")


def main():
    """主函数"""
    try:
        # 测试流程管理器
        test_flow_manager()
        
        # 询问是否测试GUI
        response = input("\n是否测试GUI组件? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            test_gui_components()
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
