"""
自动化流程管理器
用于管理自动化流程定义和浏览器绑定关系
"""

import json
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class AutomationFlowManager:
    """自动化流程管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """
        初始化流程管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.flows_file = self.config_dir / "automation_flows.json"
        self.main_config_file = self.config_dir / "config.json"
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        self._flows_cache = {}
        self._main_config_cache = {}
    
    def load_flows(self) -> Dict[str, Any]:
        """
        加载自动化流程配置
        
        Returns:
            Dict[str, Any]: 流程配置数据
        """
        try:
            if self.flows_file.exists():
                with open(self.flows_file, 'r', encoding='utf-8') as f:
                    self._flows_cache = json.load(f)
                    logger.info("自动化流程配置加载成功")
            else:
                logger.warning("流程配置文件不存在，使用默认配置")
                self._flows_cache = self._get_default_flows()
                self.save_flows(self._flows_cache)
                
            return self._flows_cache.copy()
            
        except json.JSONDecodeError as e:
            logger.error(f"流程配置文件格式错误: {e}")
            return self._get_default_flows()
        except Exception as e:
            logger.error(f"加载流程配置文件时发生错误: {e}")
            return self._get_default_flows()
    
    def save_flows(self, flows_config: Dict[str, Any]) -> bool:
        """
        保存自动化流程配置
        
        Args:
            flows_config: 流程配置数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(self.flows_file, 'w', encoding='utf-8') as f:
                json.dump(flows_config, f, ensure_ascii=False, indent=4)
            
            self._flows_cache = flows_config.copy()
            logger.info("流程配置文件保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存流程配置文件时发生错误: {e}")
            return False
    
    def load_main_config(self) -> Dict[str, Any]:
        """
        加载主配置文件
        
        Returns:
            Dict[str, Any]: 主配置数据
        """
        try:
            if self.main_config_file.exists():
                with open(self.main_config_file, 'r', encoding='utf-8') as f:
                    self._main_config_cache = json.load(f)
                    logger.info("主配置文件加载成功")
            else:
                logger.warning("主配置文件不存在")
                self._main_config_cache = {}
                
            return self._main_config_cache.copy()
            
        except json.JSONDecodeError as e:
            logger.error(f"主配置文件格式错误: {e}")
            return {}
        except Exception as e:
            logger.error(f"加载主配置文件时发生错误: {e}")
            return {}
    
    def save_main_config(self, config: Dict[str, Any]) -> bool:
        """
        保存主配置文件
        
        Args:
            config: 主配置数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            with open(self.main_config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            
            self._main_config_cache = config.copy()
            logger.info("主配置文件保存成功")
            return True
            
        except Exception as e:
            logger.error(f"保存主配置文件时发生错误: {e}")
            return False
    
    def get_available_flows(self) -> List[Dict[str, Any]]:
        """
        获取可用的自动化流程列表
        
        Returns:
            List[Dict[str, Any]]: 可用流程列表
        """
        flows_config = self.load_flows()
        flows = flows_config.get("flows", {})
        
        available_flows = []
        for flow_id, flow_data in flows.items():
            if flow_data.get("enabled", True):
                available_flows.append({
                    "id": flow_id,
                    "name": flow_data.get("name", "未命名流程"),
                    "description": flow_data.get("description", ""),
                    "type": flow_data.get("type", "unknown")
                })
        
        return available_flows
    
    def get_browser_flow_binding(self, browser_id: str) -> Optional[str]:
        """
        获取浏览器的流程绑定
        
        Args:
            browser_id: 浏览器ID
            
        Returns:
            Optional[str]: 绑定的流程ID，未绑定时返回None
        """
        flows_config = self.load_flows()
        bindings = flows_config.get("browser_bindings", {})
        return bindings.get(browser_id)
    
    def set_browser_flow_binding(self, browser_id: str, flow_id: str) -> bool:
        """
        设置浏览器的流程绑定
        
        Args:
            browser_id: 浏览器ID
            flow_id: 流程ID
            
        Returns:
            bool: 是否设置成功
        """
        flows_config = self.load_flows()
        
        if "browser_bindings" not in flows_config:
            flows_config["browser_bindings"] = {}
        
        flows_config["browser_bindings"][browser_id] = flow_id
        flows_config["last_updated"] = self._get_current_timestamp()
        
        return self.save_flows(flows_config)
    
    def remove_browser_flow_binding(self, browser_id: str) -> bool:
        """
        移除浏览器的流程绑定
        
        Args:
            browser_id: 浏览器ID
            
        Returns:
            bool: 是否移除成功
        """
        flows_config = self.load_flows()
        bindings = flows_config.get("browser_bindings", {})
        
        if browser_id in bindings:
            del bindings[browser_id]
            flows_config["last_updated"] = self._get_current_timestamp()
            return self.save_flows(flows_config)
        
        return True  # 如果不存在绑定，认为移除成功
    
    def get_browsers_with_flows(self) -> List[Dict[str, Any]]:
        """
        获取所有浏览器及其流程绑定信息
        
        Returns:
            List[Dict[str, Any]]: 浏览器和流程绑定信息列表
        """
        main_config = self.load_main_config()
        flows_config = self.load_flows()
        
        browsers = []
        browser_data = main_config.get("browser_data", {}).get("browsers", {})
        bindings = flows_config.get("browser_bindings", {})
        flows = flows_config.get("flows", {})
        
        for profile_id, data in browser_data.items():
            bound_flow_id = bindings.get(profile_id, "none")
            bound_flow_name = "未配置"
            
            if bound_flow_id in flows:
                bound_flow_name = flows[bound_flow_id].get("name", "未知流程")
            
            browser_info = {
                "id": str(profile_id),
                "name": data.get("name", ""),
                "group_name": data.get("group_name", ""),
                "bound_flow_id": bound_flow_id,
                "bound_flow_name": bound_flow_name,
                "last_open_time": data.get("last_open_time", 0)
            }
            browsers.append(browser_info)
        
        return browsers
    
    def _get_default_flows(self) -> Dict[str, Any]:
        """获取默认流程配置"""
        return {
            "flows": {
                "none": {
                    "id": "none",
                    "name": "无选择",
                    "description": "不分配任何自动化流程",
                    "type": "none",
                    "enabled": True
                },
                "youtube_shorts": {
                    "id": "youtube_shorts",
                    "name": "流程A：YouTube Shorts 自动化",
                    "description": "自动观看、点赞、订阅 YouTube Shorts 视频",
                    "type": "youtube",
                    "enabled": True
                },
                "docker_automation": {
                    "id": "docker_automation", 
                    "name": "流程B：Docker 网站访问",
                    "description": "自动访问指定网站列表",
                    "type": "docker",
                    "enabled": True
                }
            },
            "browser_bindings": {},
            "last_updated": None
        }
    
    def _get_current_timestamp(self) -> int:
        """获取当前时间戳"""
        import time
        return int(time.time())
